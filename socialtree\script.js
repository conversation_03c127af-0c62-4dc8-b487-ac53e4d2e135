// Premium Dark Blue Animated Background
// Elegant and sophisticated background with subtle animations

(function(){
  document.addEventListener('DOMContentLoaded', () => {
    console.log('Initializing Premium Dark Blue Background...');

    // Create background container
    const backgroundContainer = document.createElement('div');
    backgroundContainer.id = 'premium-background';
    backgroundContainer.setAttribute('aria-hidden', 'true');

    // Insert at the beginning of body
    document.body.insertBefore(backgroundContainer, document.body.firstChild);

    // Create multiple animated layers
    backgroundContainer.innerHTML = `
      <div class="bg-layer bg-base"></div>
      <div class="bg-layer bg-gradient-1"></div>
      <div class="bg-layer bg-gradient-2"></div>
      <div class="bg-layer bg-particles"></div>
      <div class="bg-layer bg-glow"></div>
    `;

    // Add premium dark blue background styles
    const style = document.createElement('style');
    style.textContent = `
      #premium-background {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        z-index: -10;
        overflow: hidden;
      }

      .bg-layer {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
      }

      /* Base dark blue gradient */
      .bg-base {
        background: linear-gradient(135deg,
          #0a0e27 0%,
          #1a1f3a 25%,
          #0f1629 50%,
          #1e2347 75%,
          #0d1426 100%);
      }

      /* Animated gradient overlay 1 */
      .bg-gradient-1 {
        background: radial-gradient(ellipse 120% 80% at 50% 0%,
          rgba(30, 58, 138, 0.3) 0%,
          rgba(15, 23, 42, 0.2) 40%,
          transparent 70%);
        animation: gradientFloat1 20s ease-in-out infinite;
      }

      /* Animated gradient overlay 2 */
      .bg-gradient-2 {
        background: radial-gradient(ellipse 100% 60% at 50% 100%,
          rgba(59, 130, 246, 0.15) 0%,
          rgba(30, 41, 59, 0.1) 50%,
          transparent 80%);
        animation: gradientFloat2 25s ease-in-out infinite reverse;
      }

      /* Subtle particle effect */
      .bg-particles {
        background-image:
          radial-gradient(circle at 20% 30%, rgba(147, 197, 253, 0.1) 1px, transparent 1px),
          radial-gradient(circle at 80% 70%, rgba(96, 165, 250, 0.08) 1px, transparent 1px),
          radial-gradient(circle at 40% 80%, rgba(59, 130, 246, 0.06) 1px, transparent 1px),
          radial-gradient(circle at 90% 20%, rgba(147, 197, 253, 0.05) 1px, transparent 1px),
          radial-gradient(circle at 10% 90%, rgba(96, 165, 250, 0.04) 1px, transparent 1px);
        background-size:
          200px 200px,
          300px 300px,
          250px 250px,
          180px 180px,
          220px 220px;
        background-position:
          0 0,
          50px 50px,
          100px 25px,
          150px 75px,
          25px 125px;
        animation: particlesDrift 30s linear infinite;
      }

      /* Ambient glow effect */
      .bg-glow {
        background:
          radial-gradient(ellipse 800px 400px at 30% 40%, rgba(59, 130, 246, 0.08) 0%, transparent 60%),
          radial-gradient(ellipse 600px 300px at 70% 60%, rgba(147, 197, 253, 0.06) 0%, transparent 50%);
        animation: ambientGlow 18s ease-in-out infinite;
      }

      /* Animations */
      @keyframes gradientFloat1 {
        0%, 100% {
          transform: translateY(0%) scale(1);
          opacity: 0.8;
        }
        50% {
          transform: translateY(-5%) scale(1.05);
          opacity: 0.6;
        }
      }

      @keyframes gradientFloat2 {
        0%, 100% {
          transform: translateY(0%) scale(1);
          opacity: 0.7;
        }
        50% {
          transform: translateY(3%) scale(0.95);
          opacity: 0.9;
        }
      }

      @keyframes particlesDrift {
        0% {
          transform: translate(0, 0);
        }
        100% {
          transform: translate(-50px, -50px);
        }
      }

      @keyframes ambientGlow {
        0%, 100% {
          opacity: 0.4;
          transform: scale(1);
        }
        33% {
          opacity: 0.6;
          transform: scale(1.1);
        }
        66% {
          opacity: 0.3;
          transform: scale(0.9);
        }
      }

      /* Accessibility - respect reduced motion */
      @media (prefers-reduced-motion: reduce) {
        .bg-layer {
          animation: none !important;
        }
      }

      /* Ensure background covers everything */
      body {
        position: relative;
      }
    `;

    document.head.appendChild(style);
    console.log('Premium Dark Blue Background initialized successfully');
  });
})();


